# 部门选择器组件

## 概述

部门选择器组件提供了一个功能完整的部门树形选择界面，支持搜索、多选、状态管理等功能。

## 功能特性

### ✅ 已实现功能

1. **搜索功能**
   - 实时搜索部门名称
   - 保持层级结构完整（匹配部门的上级部门自动可见）
   - 支持中文搜索

2. **Provider 状态管理**
   - 使用 `DepartmentSelectorProvider` 管理所有状态
   - 支持部门数据加载、错误处理
   - 管理搜索状态、选中状态、展开状态

3. **交互功能**
   - 单选模式（高亮显示）
   - 多选模式（复选框）
   - 展开/折叠节点
   - 级联选择（父子节点联动）

4. **UI 特性**
   - 响应式设计
   - 加载状态指示
   - 错误状态处理
   - 空数据提示

## 使用方法

### 基本使用

```dart
import 'package:provider/provider.dart';
import 'package:octasync_client/components/selector/department_selector/department_tree.dart';
import 'package:octasync_client/components/selector/department_selector/department_selector_provider.dart';

class MyPage extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DepartmentSelectorProvider(),
      child: DepartmentTree(
        showCheckbox: true,
        onNodeTap: (department) {
          print('点击了部门: ${department.departmentName}');
        },
        onNodeSelected: (department, isSelected) {
          print('${isSelected ? '选中' : '取消选中'}了部门: ${department.departmentName}');
        },
      ),
    );
  }
}
```

### 带搜索功能的使用

```dart
class DepartmentSelectorWithSearch extends StatefulWidget {
  @override
  State<DepartmentSelectorWithSearch> createState() => _DepartmentSelectorWithSearchState();
}

class _DepartmentSelectorWithSearchState extends State<DepartmentSelectorWithSearch> {
  final GlobalKey<DepartmentTreeState> _treeKey = GlobalKey<DepartmentTreeState>();
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (_) => DepartmentSelectorProvider(),
      child: Column(
        children: [
          // 搜索栏
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索部门名称...',
              prefixIcon: Icon(Icons.search),
            ),
            onChanged: (query) => _treeKey.currentState?.search(query),
          ),
          
          // 部门树
          Expanded(
            child: DepartmentTree(
              key: _treeKey,
              showCheckbox: true,
              onNodeTap: (department) {
                // 处理节点点击
              },
              onNodeSelected: (department, isSelected) {
                // 处理节点选择
              },
            ),
          ),
        ],
      ),
    );
  }
}
```

### 获取选中的部门

```dart
// 方法1：通过 Provider
final provider = context.read<DepartmentSelectorProvider>();
final selectedDepartments = provider.getSelectedDepartments();

// 方法2：通过组件实例
final selectedDepartments = _treeKey.currentState?.getAllCheckedDepartments() ?? [];
```

### 监听状态变化

```dart
Consumer<DepartmentSelectorProvider>(
  builder: (context, provider, child) {
    return Column(
      children: [
        Text('已选择: ${provider.selectedDepartmentIds.length} 个部门'),
        Text('搜索关键词: ${provider.searchQuery ?? "无"}'),
        if (provider.isLoading) CircularProgressIndicator(),
        if (provider.errorMessage != null) Text('错误: ${provider.errorMessage}'),
      ],
    );
  },
)
```

## API 参考

### DepartmentTree

| 属性 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `showCheckbox` | `bool` | `false` | 是否显示复选框 |
| `onNodeTap` | `Function(DepartmentModel)?` | `null` | 节点点击回调 |
| `onNodeSelected` | `Function(DepartmentModel, bool)?` | `null` | 节点选择回调 |

### DepartmentTreeState 公开方法

| 方法 | 说明 |
|------|------|
| `refresh()` | 刷新部门数据 |
| `search(String? query)` | 搜索部门 |
| `getAllCheckedDepartments()` | 获取所有选中的部门 |

### DepartmentSelectorProvider

#### 属性

| 属性 | 类型 | 说明 |
|------|------|------|
| `allDepartments` | `List<DepartmentModel>` | 所有部门数据 |
| `filteredDepartments` | `List<DepartmentModel>` | 过滤后的部门数据 |
| `searchQuery` | `String?` | 当前搜索查询 |
| `isLoading` | `bool` | 加载状态 |
| `errorMessage` | `String?` | 错误信息 |
| `selectedDepartmentIds` | `Set<String>` | 选中的部门ID集合 |

#### 方法

| 方法 | 说明 |
|------|------|
| `loadDepartments()` | 加载部门数据 |
| `refresh()` | 刷新部门数据 |
| `search(String? query)` | 搜索部门 |
| `toggleDepartmentSelection(String id)` | 切换部门选中状态 |
| `clearAllSelections()` | 清除所有选中状态 |
| `getSelectedDepartments()` | 获取选中的部门对象列表 |

## 搜索算法

搜索功能采用层级保持算法：

1. **匹配检测**：检查部门名称是否包含搜索关键词
2. **层级保持**：当部门匹配时，自动包含其所有上级部门
3. **结果过滤**：返回匹配部门及其上级部门的完整层级结构

这确保了搜索结果始终保持完整的树形结构，用户可以清楚地看到匹配部门在组织架构中的位置。

## 注意事项

1. **Provider 依赖**：组件必须在 `DepartmentSelectorProvider` 的上下文中使用，否则会显示错误提示
2. **错误处理**：如果没有正确配置 Provider，组件会显示友好的错误信息而不是崩溃
3. **搜索性能**：大量部门数据时，搜索可能有轻微延迟，这是正常现象
4. **状态管理**：所有状态都通过 Provider 管理，避免直接修改组件内部状态
5. **回调处理**：`onNodeTap` 和 `onNodeSelected` 回调是可选的，根据需要使用
6. **调试信息**：当 Provider 不可用时，会在调试控制台输出相关信息

## 故障排除

### 常见问题

#### 1. ProviderNotFoundException 错误
**错误信息**：`Could not find the correct Provider<DepartmentSelectorProvider>`

**解决方案**：
```dart
// ❌ 错误用法 - 没有 Provider
Widget build(BuildContext context) {
  return DepartmentTree(); // 会报错
}

// ✅ 正确用法 - 包裹 Provider
Widget build(BuildContext context) {
  return ChangeNotifierProvider(
    create: (_) => DepartmentSelectorProvider(),
    child: DepartmentTree(),
  );
}
```

#### 2. 搜索功能不工作
**检查**：
- 确保调用了 `search()` 方法
- 确保组件在 Provider 上下文中
- 检查控制台是否有调试信息

#### 3. 选中状态不同步
**解决**：使用 Provider 的状态管理方法而不是直接操作组件状态

## 示例

完整的使用示例请参考：
- `department_selector_example.dart` - 完整功能示例
- `SimpleDepartmentSelectorExample` - 简单使用示例
