import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:octasync_client/models/department/department_model.dart';
import 'department_tree_style.dart';
import 'department_selector_provider.dart';

/// 部门树节点数据模型
class DepartmentTreeNode {
  final DepartmentModel department;
  final List<DepartmentTreeNode> children;
  bool isExpanded;
  bool isSelected; // 节点点击选择状态（高亮显示）
  bool isChecked; // 复选框选中状态
  bool isIndeterminate; // 复选框半选状态

  DepartmentTreeNode({
    required this.department,
    List<DepartmentTreeNode>? children,
    this.isExpanded = false,
    this.isSelected = false,
    this.isChecked = false,
    this.isIndeterminate = false,
  }) : children = children ?? [];

  /// 检查是否为叶子节点
  bool get isLeaf => children.isEmpty;

  /// 获取节点层级深度
  int get level {
    if (department.parentIdList.isEmpty) return 0;
    return department.parentIdList.length;
  }

  /// 获取所有被选中的子节点数量
  int get checkedChildrenCount {
    return children.where((child) => child.isChecked).length;
  }

  /// 获取所有子节点数量
  int get totalChildrenCount {
    return children.length;
  }

  /// 检查是否有部分子节点被选中
  bool get hasPartiallyCheckedChildren {
    if (children.isEmpty) return false;
    int checkedCount = checkedChildrenCount;
    return checkedCount > 0 && checkedCount < totalChildrenCount;
  }

  /// 检查是否所有子节点都被选中
  bool get hasAllChildrenChecked {
    if (children.isEmpty) return false;
    return checkedChildrenCount == totalChildrenCount;
  }
}

///树状选择器组件
class DepartmentTree extends StatefulWidget {
  /// 是否显示复选框
  final bool showCheckbox;

  /// 节点点击回调
  final void Function(DepartmentModel department)? onNodeTap;

  /// 节点选择回调（复选框点击时触发）
  final void Function(DepartmentModel department, bool isSelected)? onNodeSelected;

  const DepartmentTree({super.key, this.showCheckbox = false, this.onNodeTap, this.onNodeSelected});

  @override
  State<DepartmentTree> createState() => DepartmentTreeState();
}

// 将 State 类改为公开，以便外部可以访问
class DepartmentTreeState extends State<DepartmentTree> {
  /// 刷新部门列表数据 - 公开方法供外部调用
  void refresh() {
    try {
      context.read<DepartmentSelectorProvider>().refresh();
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
    }
  }

  /// 搜索部门 - 公开方法供外部调用
  ///
  /// [searchQuery] 搜索关键词，null或空字符串时显示所有部门
  void search(String? searchQuery) {
    try {
      context.read<DepartmentSelectorProvider>().search(searchQuery);
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
    }
  }

  /// 处理节点点击事件（仅影响高亮显示，不影响复选框状态）
  void _handleNodeTap(DepartmentTreeNode node) {
    try {
      final provider = context.read<DepartmentSelectorProvider>();
      provider.setHighlightedDepartment(node.department.id);
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
    }

    // 触发回调
    widget.onNodeTap?.call(node.department);
  }

  /// 处理节点展开/折叠
  void _handleNodeToggle(DepartmentTreeNode node) {
    try {
      final provider = context.read<DepartmentSelectorProvider>();
      provider.toggleDepartmentExpansion(node.department.id!);
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
    }
  }

  /// 处理复选框选择
  void _handleNodeCheckboxChanged(DepartmentTreeNode node, bool? value) {
    try {
      final provider = context.read<DepartmentSelectorProvider>();
      bool newCheckedState = value ?? false;
      provider.toggleDepartmentSelection(node.department.id!, forceValue: newCheckedState);

      // 触发回调
      widget.onNodeSelected?.call(node.department, newCheckedState);
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
    }
  }

  /// 获取所有被选中的部门（公开方法，供外部调用）
  List<DepartmentModel> getAllCheckedDepartments() {
    try {
      return context.read<DepartmentSelectorProvider>().getSelectedDepartments();
    } catch (e) {
      debugPrint('DepartmentTree: 无法找到 DepartmentSelectorProvider');
      return [];
    }
  }

  @override
  Widget build(BuildContext context) {
    // 检查是否存在 DepartmentSelectorProvider
    try {
      final provider = context.watch<DepartmentSelectorProvider>();

      if (provider.isLoading) {
        return Container(
          decoration: DepartmentTreeStyles.containerDecoration(context),
          child: const Center(child: CircularProgressIndicator()),
        );
      }

      if (provider.errorMessage != null) {
        return Container(
          decoration: DepartmentTreeStyles.containerDecoration(context),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  provider.errorMessage!,
                  style: TextStyle(color: Theme.of(context).colorScheme.error),
                ),
                const SizedBox(height: 16),
                ElevatedButton(onPressed: () => provider.refresh(), child: const Text('重试')),
              ],
            ),
          ),
        );
      }

      final treeNodes = _buildTreeNodesFromProvider(provider);
      return Container(
        decoration: DepartmentTreeStyles.containerDecoration(context),
        child:
            treeNodes.isEmpty
                ? const Center(child: Text('暂无数据'))
                : ListView(
                  padding: DepartmentTreeStyles.listViewPadding,
                  children: _buildTreeNodes(treeNodes),
                ),
      );
    } catch (e) {
      // 如果没有找到 Provider，显示错误提示
      return Container(
        decoration: DepartmentTreeStyles.containerDecoration(context),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 48, color: Theme.of(context).colorScheme.error),
              const SizedBox(height: 16),
              Text(
                'DepartmentTree 组件需要在 DepartmentSelectorProvider 中使用',
                style: TextStyle(color: Theme.of(context).colorScheme.error),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                '请确保组件被 ChangeNotifierProvider<DepartmentSelectorProvider> 包裹',
                style: Theme.of(context).textTheme.bodySmall,
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
  }

  /// 从 Provider 构建树节点
  List<DepartmentTreeNode> _buildTreeNodesFromProvider(DepartmentSelectorProvider provider) {
    final filteredDepartments = provider.filteredDepartments;

    // 创建ID到节点的映射
    Map<String, DepartmentTreeNode> nodeMap = {};

    // 首先创建所有节点
    for (var dept in filteredDepartments) {
      if (dept.id != null) {
        nodeMap[dept.id!] = DepartmentTreeNode(
          department: dept,
          children: <DepartmentTreeNode>[],
          isExpanded: provider.isDepartmentExpanded(dept.id!),
          isSelected: provider.isDepartmentHighlighted(dept.id!),
          isChecked: provider.isDepartmentSelected(dept.id!),
        );
      }
    }

    // 构建父子关系
    List<DepartmentTreeNode> rootNodes = [];

    for (var dept in filteredDepartments) {
      if (dept.id == null) continue;

      var currentNode = nodeMap[dept.id!]!;

      if (dept.parentId == null || dept.parentId!.isEmpty) {
        // 根节点
        rootNodes.add(currentNode);
      } else {
        // 子节点
        var parentNode = nodeMap[dept.parentId];
        if (parentNode != null) {
          parentNode.children.add(currentNode);
        }
      }
    }

    return rootNodes;
  }

  /// 构建树节点列表
  List<Widget> _buildTreeNodes(List<DepartmentTreeNode> nodes) {
    List<Widget> widgets = [];

    for (var node in nodes) {
      widgets.add(_buildTreeNodeWidget(node));

      // 如果节点展开且有子节点，递归构建子节点
      if (node.isExpanded && node.children.isNotEmpty) {
        widgets.addAll(_buildTreeNodes(node.children));
      }
    }

    return widgets;
  }

  /// 构建单个树节点组件
  Widget _buildTreeNodeWidget(DepartmentTreeNode node) {
    final int indentLevel = node.level;
    final bool hasChildren = node.children.isNotEmpty;
    final bool isSelected = node.isSelected;

    return Container(
      margin: DepartmentTreeStyles.nodeContainerMargin(indentLevel),
      child: Material(
        color: DepartmentTreeStyles.nodeMaterialColor(context, isSelected),
        borderRadius: DepartmentTreeStyles.nodeBorderRadius,
        child: InkWell(
          onTap: () => _handleNodeTap(node),
          hoverColor: DepartmentTreeStyles.nodeHoverColor(context),
          splashColor: DepartmentTreeStyles.nodeSplashColor(context),
          borderRadius: DepartmentTreeStyles.nodeBorderRadius,
          child: Container(
            height: DepartmentTreeStyles.nodeHeight,
            padding: DepartmentTreeStyles.nodeContainerPadding,
            child: Row(
              children: [
                // 展开/折叠图标区域 - 固定宽度确保对齐
                SizedBox(
                  width: DepartmentTreeStyles.expandIconSize.width,
                  height: DepartmentTreeStyles.expandIconSize.height,
                  child:
                      hasChildren
                          ? GestureDetector(
                            onTap: () => _handleNodeToggle(node),
                            child: Center(
                              child: AnimatedRotation(
                                turns: node.isExpanded ? 0.5 : 0,
                                duration: DepartmentTreeStyles.expandAnimationDuration,
                                child: Icon(
                                  DepartmentTreeStyles.expandIcon,
                                  size: DepartmentTreeStyles.expandIconSizeValue,
                                  color: DepartmentTreeStyles.expandIconColor(context),
                                ),
                              ),
                            ),
                          )
                          : null, // 叶子节点不显示图标，但保持占位空间
                ),

                DepartmentTreeStyles.iconCheckboxSpacing,

                // 复选框（可选）- 固定高度，支持三种状态
                if (widget.showCheckbox) ...[
                  SizedBox(
                    height: DepartmentTreeStyles.checkboxHeight,
                    child: Checkbox(
                      value: node.isIndeterminate ? null : node.isChecked,
                      tristate: true, // 启用三态支持
                      onChanged: (value) => _handleNodeCheckboxChanged(node, value),
                    ),
                  ),
                  DepartmentTreeStyles.checkboxTextSpacing,
                ],

                // 部门名称 - 垂直居中对齐
                Expanded(
                  child: Align(
                    alignment: DepartmentTreeStyles.textAlignment,
                    child: Text(
                      node.department.departmentName,
                      style: DepartmentTreeStyles.departmentNameTextStyle(context, isSelected),
                      overflow: DepartmentTreeStyles.textOverflow,
                      maxLines: DepartmentTreeStyles.textMaxLines,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
